package com.employee.employee_service.controller;

import com.employee.employee_service.dto.ResponseDTO;
import com.employee.employee_service.service.EmployeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/employee")
public class EmployeeController {

    @Autowired
    private EmployeeService employeeService;

    @GetMapping("/getById/{id}")
    public ResponseDTO getEmployeeWithDepartment(@PathVariable Long id) {
        return employeeService.getById(id);
    }
}
