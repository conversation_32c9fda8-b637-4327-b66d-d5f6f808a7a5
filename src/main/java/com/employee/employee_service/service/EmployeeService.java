package com.employee.employee_service.service;

import com.employee.employee_service.dto.*;
import com.employee.employee_service.entity.Employee;
import com.employee.employee_service.repository.EmployeeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
public class EmployeeService {

    @Autowired
    private EmployeeRepository repo;

    @Autowired
    private RestTemplate restTemplate;

    public ResponseDTO getById(Long id) {
        Employee emp= repo.findById(id).get();
        EmployeeDTO empdto=new EmployeeDTO(emp.getEmpId(),emp.getEmpName(),
                emp.getMailId(),emp.getSalary());
       DepartmentDTO deptdto =    restTemplate.
               getForEntity(
                       "http://localhost:8089/api/dept/getById/"+emp.getDeptId(),
                       DepartmentDTO.class)
               .getBody();
//feign client
        ResponseDTO resdto=new ResponseDTO(empdto,deptdto);
        return  resdto;
    }
}
