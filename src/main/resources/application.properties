spring.application.name=employee-service
server.port=8088

# H2 In-Memory Database (for testing/development)
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=kky
spring.datasource.password=1234
spring.h2.console.enabled=true

# JPA/Hibernate properties
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# MySQL config (commented out - uncomment when MySQL is available)
# spring.datasource.url=***************************************
# spring.datasource.username=root
# spring.datasource.password=root
# spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
